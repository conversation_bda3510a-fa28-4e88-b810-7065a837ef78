<%
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
  set_active_tab "settings"
  js_bundle :account_settings
  css_bundle :account_settings, :reports, :tinymce
  provide :page_title, t(:page_title, "Account Settings")

  js_env(
    :ACCOUNT => {
      'id' => @context.id,
      'site_admin' => @account.site_admin?,
      'root_account' => @account.root_account?
    }.tap do |h|
      if can_do(@context, @current_user, :manage_storage_quotas)
        h.merge!(
          'default_storage_quota_mb' => @context.default_storage_quota_mb,
          'default_user_storage_quota_mb' => @context.default_user_storage_quota_mb,
          'default_group_storage_quota_mb' => @context.default_group_storage_quota_mb
        )
      end
    end
  )
%>
<% provide :right_side do %>
  <%= render :partial => "additional_settings_right_side" %>
<% end %>
<h1 class='screenreader-only'><%= t(:page_header_title, "Account Settings") %></h1>

<style type="text/css" media="screen">
  fieldset {
    margin: 2em 0;
  }
  fieldset.nested {
    margin-top: 0;
    margin-<%= direction('left') %>: 20px;
  }
  legend{
    font-size: 1.1em;
  }
</style>

<div id="account_settings_tabs" style="display:none;" class="ui-tabs-minimal">
  <ul>
    <% if can_do(@context, @current_user, :manage_account_settings) %>
    <li><a href="#tab-basic-settings" id="tab-basic-settings-link"><%= t(:tab_basic_settings, "Basic Settings") %></a></li>
    <li><a href="#tab-features" id="tab-features-link"><%= t(:tab_features, "Features") %></a></li>
    <li><a href="#tab-profile-settings" id="tab-profile-settings-link"><%= t(:tab_profile_settings, "Profile Settings") %></a></li>
    <li><a href="#tab-course-creation" id="tab-course-creation-link"><%= t(:tab_course_creation, "Course Creation") %></a></li>
    <% if !@account.site_admin? && @account.primary_settings_root_account? && @account.feature_allowed?(:submission_comment_emojis) && @account.grants_right?(@current_user, :manage_account_settings) %>
    <li><a href="#tab-blocked-emojis" id="tab-blocked-emojis-link"><%= t(:tab_blocked_emojis, "Blocked Emojis") %></a></li>
    <% end %>
    <% end %>
    <li><a href="#tab-users" id="tab-users-link"><%= t(:tab_admins, "Admins") %></a></li>
    <% if can_do(@context, @current_user, :manage_account_settings) && @account.primary_settings_root_account? && !@account.site_admin? %>
    <li><a href="#tab-notifications" id="tab-notifications-link"><%= t(:tab_notifications, "Notifications") %></a></li>
    <% end %>
    <li><a href="#tab-announcements" id="tab-announcements-link"><%= t(:tab_announcements, "Announcements") %></a></li>
    <% if @account.root_account.feature_enabled?(:javascript_csp) && can_do(@context, @current_user, :manage_account_settings) && !@account.site_admin? %>
      <li><a href="#tab-security" id="tab-security-link"><%= t("Security") %></a></li>
    <% end %>
  </ul>

  <!-- BASIC SETTINGS TAB -->
  <% if can_do(@context, @current_user, :manage_account_settings) %>
    <div id="tab-basic-settings">
      <h2 class="screenreader-only"><%= t('headings.basic_settings', "Basic Account Settings") %></h2>
      <%= form_for :account, url: account_url(@account), html: {novalidate: true, method: :put, id: "account_basic_settings", class: "account_settings"} do |f| %>
        <fieldset>
          <legend><%= t(:account_basic_settings_title, "Basic Account Settings") %></legend>
          <table class="formtable narrow">
            <tr>
              <td><%= f.blabel :name, :en => "Account Name" %></td>
              <td><%= f.text_field :name, :class => 'same-width-as-select' %></td>
            </tr>
            <% if !@account.root_account? && (@context.sis_source_id && can_do(@context.root_account, @current_user, :read_sis) || can_do(@context.root_account, @current_user, :manage_sis)) %>
              <tr>
                <td><%= f.blabel :sis_source_id, :en => "SIS ID" %></td>
                <td>
                  <span class="course_form">
                    <% if can_do(@context.root_account, @current_user, :manage_sis) %>
                      <%= f.text_field :sis_source_id, :title => "SIS ID", :value => @context.sis_source_id %>
                    <% else %>
                      <span class="sis_source_id"><%= @context.sis_source_id %></span>
                    <% end %>
                  </span>
                </td>
              </tr>
            <% end %>
            <% if available_locales.size > 1 %>
            <tr>
              <td><%= f.blabel :default_locale, :default_language, :en => "Default Language" %></td>
              <td>
                <% no_language = t(:no_language_preference, "Not set (defaults to %{language})", :language => available_locales[infer_locale(:context => @context.parent_account)]) %>
                <%= f.select :default_locale,
                             [[no_language, nil]] + available_locales.invert.sort_by { |desc, _| Canvas::ICU.collation_key(desc) },
                             {:selected => @context.default_locale}, {:class => 'locale'} %>
                <%= render :partial => 'shared/locale_warning' %>
                <p class="aside"><%= t(:default_language_description, "This will override any browser/OS language settings. Preferred languages can still be set at the course/user level.") %></p>
              </td>
            </tr>
            <% end %>
            <% if !@account.site_admin? %>
              <%= f.fields_for :settings do |settings| %>
                <%= settings.fields_for :default_due_time do |ddt| %>
                  <tr>
                    <td><%= ddt.blabel :value, t("Default Due Time") %></td>
                    <td>
                        <%= ddt.select :value, options_for_select(default_due_time_options(@account), default_due_time_key(@account)) %>
                        <p class="aside">
                          <%= t("This influences the user interface for setting due dates. It does not change the time due for any existing assignments.") %>
                        </p>
                    </td>
                  </tr>
                 <% end %>
              <% end %>
            <% end %>
            <% if @account.primary_settings_root_account? %>
              <tr>
                <td><%= f.blabel :default_time_zone, :en => "Default Time Zone" %></td>
                <td>
                  <%= f.time_zone_select :default_time_zone, I18nTimeZone.us_zones, :model => I18nTimeZone %>
                </td>
              </tr>
              <% if @account.shard.database_server.maintenance_window_start_hour %>
              <tr>
                <td></td>
                <td> <%= render partial: 'shared/maintenance_window', locals: { database_server: @account.shard.database_server, local_zone: @account.default_time_zone } %> </td>
              </tr>
              <% end %>
              <%= f.fields_for :settings do |settings| %>
                <% if @account.grants_right?(@current_user, :manage_site_settings) %>
                  <tr>
                    <td><%= settings.blabel :mfa_settings, :en => "Multi-Factor Authentication" %></td>
                    <td>
                      <%= settings.select :mfa_settings, [[t('select.mfa.disabled', "Disabled"), :disabled],
                                                          [t('select.mfa.optional', "Optional"), :optional],
                                                          [t('select.mfa.required_for_admins', "Required for Admins"), :required_for_admins],
                                                          [t('select.mfa.required', "Required"), :required]], :selected => @account.mfa_settings %>
                      <p>
                        <%= settings.check_box :mobile_qr_login_is_enabled, checked: @account.mobile_qr_login_is_enabled? %>
                        <%= settings.label :mobile_qr_login_is_enabled, en: "Allow logins to the mobile apps via the use of QR codes" %>
                      </p>
                    </td>
                  </tr>
                <% end %>
                <% unless @account.site_admin? %>
                  <tr>
                    <td><%= settings.blabel :self_enrollment, :en => "Allow Self-Enrollment" %></td>
                    <td>
                      <%= settings.select :self_enrollment, [
                              [t(:never_allow_self_enrollment_option, 'Never'), ''],
                              [t(:self_enroll_for_manually_created_courses_option, 'For Manually-Created Courses'), 'manually_created'],
                              [t(:self_enroll_for_any_courses_option, 'For Any Courses'), 'any']
                        ], :selected => @account.settings[:self_enrollment] %>
                    </td>
                  </tr>
                  <tr>
                    <td><%= settings.blabel :trusted_referers, :en => "Trusted HTTP Referers" %></td>
                    <td>
                      <%= settings.text_field :trusted_referers,
                                              :value => @account.settings[:trusted_referers],
                                              :class => 'same-width-as-select',
                                              :placeholder => "https://example.edu" %>
                      <p class="aside"><%= t("This is a comma separated list of URL's to trust.  Trusting any URL's in this list will bypass the CSRF token when logging in to Canvas.") %></p>
                    </td>
                  </tr>
                  <% if can_do(@context, @current_user, :manage_account_settings) %>
                    <tr>
                      <td><%= settings.blabel :default_dashboard_view, t("Default View for Dashboard") %></td>
                      <td>
                        <%= settings.select :default_dashboard_view, options_for_select(dashboard_view_options, @account.default_dashboard_view) %>
                        <p class="aside"><%= settings.check_box :force_default_dashboard_view, :checked => false %>  <%= settings.label :force_default_dashboard_view, t("Overwrite all users' existing default dashboard preferences") %></p>
                      </td>
                    </tr>
                  <% end %>
                <% end %>
              <% end %>
            <% end %>
          </table>
        </fieldset>

        <%= render partial: 'additional_settings', locals: { f: f } %>

        <footer class="sticky-footer">
          <div class="form-actions-sticky-footer">
              <button type="submit" class="Button Button--primary"><%= t(:update_settings_button, "Update Settings") %></button>
          </div>
        </footer>
      <% end %>
    </div>
  <% end %>

  <!-- FEATURES TAB -->
  <% if can_do(@context, @current_user, :manage_account_settings) %>
    <div id="tab-features">
      <h2 class="screenreader-only"><%= t('headings.features', "Feature Settings") %></h2>
      <%= form_for :account, url: account_url(@account), html: {novalidate: true, method: :put, id: "account_feature_settings", class: "account_settings"} do |f| %>
        <fieldset>
          <legend><%= t(:features_title, "Features") %></legend>
          <%= f.fields_for :settings do |settings| %>
            <div>
              <% k5_settings = @account.enable_as_k5_account %>
              <% disabled = k5_settings[:locked] && k5_settings[:inherited] %>
              <%= settings.fields_for :enable_as_k5_account do |k5| %>
                <%= k5.check_box :value, :checked => k5_settings[:value], :disabled => disabled %>
                <%= k5.label :value, t("Canvas for Elementary") %>
              <% end %>
              <div id="k5_account_warning_message" class="account_settings__k5-help-text">
                <i class='icon-warning-borderless'></i>
                <div class="account_settings__k5-help-text-container">
                  <p><%= t ("This setting significantly changes the Canvas experience for students and teachers.")%></p>
                  <p>
                    <%= t do %>
                      To learn more about Canvas for Elementary, please refer to <a href="<%= t(:'#community.canvas_for_elementary') %>" target="_blank">our documentation</a>.
                    <% end %>
                  </p>
                  <% font_settings = @account.use_classic_font_in_k5 %>
                  <%= settings.fields_for :use_classic_font_in_k5 do |font| %>
                    <div>
                      <span class="screenreader-only"><%= t('Canvas for Elementary Font Selection') %></span>
                      <%= font.radio_button :value, false, checked: !font_settings[:value], disabled: disabled, id: "k5_font_disabled_radio" %>
                      <%= font.label :value, t("Large Balsamiq Font"), for: "k5_font_disabled_radio", style: "margin-bottom: 0;" %>
                      <br />
                      <%= font.radio_button :value, true, checked: font_settings[:value], disabled: disabled, id: "k5_font_enabled_radio" %>
                      <%= font.label :value, t("Regular Lato Font"), for: "k5_font_enabled_radio" %>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>

            <% if !@account.site_admin? && @account.feature_enabled?(:differentiation_tags) && @account.feature_enabled?(:assign_to_differentiation_tags) %>
              <div>
                <% diff_tags = @account.allow_assign_to_differentiation_tags %>
                <% disabled = diff_tags[:locked] && diff_tags[:inherited] %>
                <%= settings.fields_for :allow_assign_to_differentiation_tags do |diff| %>
                  <%= diff.check_box :value, checked: diff_tags[:value], disabled: disabled %>
                  <%= diff.label :value, t("Differentiation Tags") %>
                <% end %>
                <div class="account_settings__help-text">
                  <%= t("Differentiation Tags allow instructors to organize students by tags for a more personalized learning experience.") %>
                </div>
                <% unless disabled %>
                  <div class="sub_checkbox" style="margin-left: 20px;">
                    <%= settings.fields_for :allow_assign_to_differentiation_tags do |diff_lock| %>
                      <%= diff_lock.check_box :locked, checked: diff_tags[:locked] %>
                      <%= diff_lock.label :locked, t("Lock this setting for sub-accounts and courses") %>
                    <% end %>
                  </div>
                <% end %>
              </div>
            <% end %>

            <% if @account.grants_right?(@current_user, :manage_account_settings) && @account.root_account.feature_enabled?(:allow_limited_access_for_students)%>
              <div>
                <%= settings.check_box :enable_limited_access_for_students, :checked => @account.enable_limited_access_for_students? %>
                <%= settings.label :enable_limited_access_for_students, :en => "Enable Limited Access for Students for this account" %>
              </div>
            <% end %>

            <% if @account.primary_settings_root_account? %>
              <% if @account.grants_right?(@current_user, :manage_site_settings) %>
                <div>
                  <%= settings.check_box :admins_can_change_passwords, :checked => @account.settings[:admins_can_change_passwords] == true %>
                  <%= settings.label :admins_can_change_passwords, :en => "Password setting by admins" %>
                </div>
                <div>
                  <%= settings.check_box :admins_can_view_notifications, :checked => @account.settings[:admins_can_view_notifications] %>
                  <%= settings.label :admins_can_view_notifications, :en => "Admins can view notifications" %>
                </div>
                <% unless @account.site_admin? %>
                  <div>
                    <%= settings.check_box :enable_eportfolios, :checked => @account.settings[:enable_eportfolios] != false %>
                    <%= settings.label :enable_eportfolios, :en => "ePortfolios" %>
                  </div>
                  <div>
                    <%= settings.check_box :allow_invitation_previews, :checked => @account.settings[:allow_invitation_previews] %>
                    <%= settings.label :allow_invitation_previews, :en => "Invitation Previews" %>
                  </div>
                  <div>
                    <%= settings.check_box :enable_alerts, :checked => @account.settings[:enable_alerts] %>
                    <%= settings.label :enable_alerts, :en => "Alerts (beta)" %>
                  </div>
                <% end %>

                <div id="global_includes_warning_message_wrapper">
                  <%= settings.check_box :global_includes, :checked => @account.settings[:global_includes] %>
                  <%= settings.label :global_includes, :en => "Custom CSS/JavaScript overrides" %>
                  <div id="global_includes_warning_message" class="account_settings__help-text">
                    <p><%= t ("Custom CSS and Javascript may cause accessibility issues or conflicts with future Canvas updates!")%></p>
                    <p>
                      <%= t do %>
                      Before implementing custom CSS or Javascript, please refer to <a href="<%= t(:'#community.admin_custom_js_css') %>" target="_blank"> our documentation</a>.
                      <% end %>
                    </p>
                  </div>
                </div>
                <div id="show_scheduler_checkbox">
                  <%= settings.check_box :show_scheduler, :checked => @account.settings[:show_scheduler] %>
                  <%= settings.label :show_scheduler, :en => "Enable Scheduler" %>
                </div>
                <div>
                  <%= settings.check_box :limit_parent_app_web_access, :checked => @account.settings[:limit_parent_app_web_access] %>
                  <%= settings.label :limit_parent_app_web_access, :en => "Limit access to Canvas web from the Canvas Parent app" %>
                </div>
              <% end %>

              <div>
                <%= settings.check_box :sub_account_includes, checked: @account.settings[:sub_account_includes] %>
                <%= settings.label :sub_account_includes, :en => "Let sub-accounts use the Theme Editor to customize their own branding" %>
              </div>
              
              <% if @account.canvas_authentication? %>
                <div>
                  <%= settings.check_box :open_registration, :checked => @account.open_registration? %>
                  <%= settings.label :open_registration, :en => "Open Registration" %>
                  <% if @account.delegated_authentication? %>
                    <button data-url="<%= canvas_login_url %>" type="button" class='Button Button--icon-action open_registration_delegated_warning_btn'>
                      <%= image_tag "warning.png", :alt => '' %>
                      <span class='screenreader-only'><%= t(:open_registration_screenreader, "Warning about Open Registration") %></span>
                    </button>
                    <div id="open_registration_mount"></div>
                  <% end %>
                </div>
              <% end %>

              <% if !@account.site_admin? && @account.root_account? && Account.site_admin.feature_enabled?(:inbox_settings) %>
                <table>
                  <% inbox_signature_block_enabled = @account.enable_inbox_signature_block? %>
                  <tr>
                    <td colspan="2">
                      <%= settings.check_box :enable_inbox_signature_block, :checked => inbox_signature_block_enabled %>
                      <%= settings.label     :enable_inbox_signature_block, :en => "Enable Inbox Signature Block" %>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="2" class="sub_checkbox">
                      <%= settings.check_box :disable_inbox_signature_block_for_students, :checked => @account.disable_inbox_signature_block_for_students?, :disabled => !inbox_signature_block_enabled %>
                      <%= settings.label     :disable_inbox_signature_block_for_students, :en => "Disable Inbox Signature Block for Students" %>
                    </td>
                  </tr>
                  <% inbox_auto_response_enabled = @account.enable_inbox_auto_response? %>
                  <tr>
                    <td colspan="2">
                      <%= settings.check_box :enable_inbox_auto_response, :checked => inbox_auto_response_enabled %>
                      <%= settings.label     :enable_inbox_auto_response, :en => "Enable Inbox Auto Response" %>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="2" class="sub_checkbox">
                      <%= settings.check_box :disable_inbox_auto_response_for_students, :checked => @account.disable_inbox_auto_response_for_students?, :disabled => !inbox_auto_response_enabled %>
                      <%= settings.label     :disable_inbox_auto_response_for_students, :en => "Disable Inbox Auto Response for Students" %>
                    </td>
                  </tr>
                </table>
              <% end %>

              <div>
                <%= settings.check_box :users_can_edit_comm_channels, :checked => @account.users_can_edit_comm_channels? %>
                <%= settings.label :users_can_edit_comm_channels, :en => "Users can edit their communication channels" %>
              </div>
              <div>
                <%= settings.check_box :edit_institution_email, :checked => @account.edit_institution_email? %>
                <%= settings.label     :edit_institution_email, :en => "Users can delete their institution-assigned email address" %>
              </div>
              <div>
                <%= settings.check_box :author_email_in_notifications, :checked => @account.author_email_in_notifications? %>
                <%= settings.label     :author_email_in_notifications, :en => "Show the email address of sender for user interaction Notifications" %>
              </div>
              <div>
                <%= settings.check_box :enable_course_catalog, :checked => @account.settings[:enable_course_catalog] %>
                <%= settings.label     :enable_course_catalog, :en => "Show a searchable list of courses in this root account with the \"Include this course in the public course index\" flag enabled." %>
              </div>
              <% if Account.site_admin.feature_enabled?(:show_push_notification_account_setting) %>
                <div>
                  <%= settings.check_box :enable_push_notifications, :checked => @account.enable_push_notifications? %>
                  <%= settings.label     :enable_push_notifications, :en => "Enable push notifications to mobile devices" %>
                </div>
              <% end %>
              <% unless @account.site_admin? %>
                <div>
                  <%= settings.check_box :kill_joy, :checked => @account.kill_joy? %>
                  <%= settings.label     :kill_joy, :en => "Remove hidden game from 404 page" %>
                </div>
              <% end %>
              <% unless @account.site_admin? %>
                <div>
                  <%= settings.check_box :suppress_notifications, :checked => @account.suppress_notifications? %>
                  <%= settings.label     :suppress_notifications, :en => "Suppress notifications from being created and sent out" %>
                </div>
              <% end %>
            <% end %>

            <% unless @account.site_admin? %>
              <div>
                <input type="checkbox" id="enable_equella" <%= 'checked' if !@account.settings[:equella_endpoint].blank? %> />
                <label for="enable_equella"><%= t 'labels.equella', 'Equella' %></label>
              </div>
            <% end %>
            <% if @account.grants_right?(@current_user, :manage_site_settings) %>
              <div>
                <%= settings.check_box :enable_turnitin, :checked => @account.settings[:enable_turnitin] %>
                <%= settings.label :enable_turnitin, :en => "Turnitin" %>
              </div>
            <% end %>
          <% end %>

          <%= f.fields_for :services do |services| %>
            <% Account.services_exposed_to_ui_hash(:setting, @current_user, @account).sort_by { |k,h| Canvas::ICU.collation_key(h[:name]) }.each do |key, service| %>
              <div>
                <%= services.check_box key, :checked => @account.service_enabled?(key) %>
                <%= services.label key, service[:name] + " " %>
              </div>
              <% if key == :avatars && @account.primary_settings_root_account? %>
                <div id="account_settings_gravatar_checkbox">
                  <%= hidden_field_tag "account[settings][enable_gravatar]", "0", id: "hidden_account_settings_enable_gravatar" %>
                  <%= check_box_tag "account[settings][enable_gravatar]", "1", @account.enable_gravatar? %>
                  <%= label_tag "account[settings][enable_gravatar]", t("Enable Gravatar") %>
                </div>
              <% end %>
            <% end %>
          <% end %>
        </fieldset>

        <footer class="sticky-footer">
          <div class="form-actions-sticky-footer">
              <button type="submit" class="Button Button--primary"><%= t(:update_settings_button, "Update Settings") %></button>
          </div>
        </footer>
      <% end %>
    </div>
  <% end %>

  <!-- PROFILE SETTINGS TAB -->
  <% if can_do(@context, @current_user, :manage_account_settings) %>
    <div id="tab-profile-settings">
      <h2 class="screenreader-only"><%= t('headings.profile_settings', "Profile Settings") %></h2>
      <%= form_for :account, url: account_url(@account), html: {novalidate: true, method: :put, id: "account_profile_settings", class: "account_settings"} do |f| %>
        <% if @account.primary_settings_root_account? %>
          <fieldset>
            <legend><%= t("Personal Profile Settings") %></legend>
            <%= f.fields_for :settings do |settings| %>
              <% if @account.grants_right?(@current_user, :manage_site_settings) %>
                <div>
                  <%= settings.check_box :enable_profiles, :checked => @account.settings[:enable_profiles] %>
                  <%= settings.label :enable_profiles, :en => "Enable Profiles" %>
                </div>
              <% end %>
              <% if !@account.site_admin? && @account.root_account? %>
                <div>
                  <%= settings.check_box :enable_name_pronunciation, :checked => @account.enable_name_pronunciation?, class: "element_toggler", "aria-controls": "name_pronunciation_options" %>
                  <%= settings.label :enable_name_pronunciation, :en => "Enable name pronunciation" %>
                  <div id="name_pronunciation_options" <%= hidden(include_style: true) unless @account.settings[:enable_name_pronunciation]%>>
                    <div style="font-size: 0.8em;"><%= t(:pronunciation_change_description, "Limit name pronunciation editing by base role") %></div>
                      <div>
                        <%= settings.check_box :allow_name_pronunciation_edit_for_admins, :checked => @account.allow_name_pronunciation_edit_for_admins? %>
                        <%= settings.label :allow_name_pronunciation_edit_for_admins, :en => "Admin" %>
                      </div>
                      <div>
                        <%= settings.check_box :allow_name_pronunciation_edit_for_teachers, :checked => @account.allow_name_pronunciation_edit_for_teachers? %>
                        <%= settings.label :allow_name_pronunciation_edit_for_teachers, :en => "Course Admin (includes Teachers, TAs, and Designers)" %>
                      </div>
                      <div>
                        <%= settings.check_box :allow_name_pronunciation_edit_for_students, :checked => @account.allow_name_pronunciation_edit_for_students? %>
                        <%= settings.label :allow_name_pronunciation_edit_for_students, :en => "Student" %>
                      </div>
                  </div>
                </div>
              <% end %>
              <% if !@account.site_admin?  %>
                <%= settings.check_box :can_add_pronouns, :checked => @account.settings[:can_add_pronouns], class: "element_toggler", "aria-controls": "personal_pronouns_options" %>
                <%= settings.label :can_add_pronouns, en: "Enable Personal Pronouns" %>
                <div id="personal_pronouns_options" <%= hidden(include_style: true) unless @account.settings[:can_add_pronouns] %>>
                  <div id="change_personal_pronouns">
                    <%= settings.check_box :can_change_pronouns, :checked => @account.can_change_pronouns? %>
                    <%= settings.label :can_change_pronouns, en: "Allow users to change their pronouns in Canvas" %>
                  </div>
                    <% js_env(PRONOUNS_LIST: ((@account.pronouns.presence || Pronouns.default_pronouns)))
                    js_bundle :available_pronouns_list %>
                </div>
              <% end %>
              <div>
                <%= settings.check_box :users_can_edit_name, :checked => @account.users_can_edit_name? %>
                <%= settings.label :users_can_edit_name, :en => "Users can edit their name" %>
              </div>
              <div>
                <%= settings.check_box :users_can_edit_profile, :checked => @account.users_can_edit_profile? %>
                <%= settings.label :users_can_edit_profile, :en => "Users can edit their profile" %>
              </div>
            <% end %>
          </fieldset>
        <% end %>

        <footer class="sticky-footer">
          <div class="form-actions-sticky-footer">
              <button type="submit" class="Button Button--primary"><%= t(:update_settings_button, "Update Settings") %></button>
          </div>
        </footer>
      <% end %>
    </div>
  <% end %>

  <!-- COURSE CREATION TAB -->
  <% if can_do(@context, @current_user, :manage_account_settings) %>
    <div id="tab-course-creation">
      <h2 class="screenreader-only"><%= t('headings.course_creation', "Course Creation Settings") %></h2>
      <%= form_for :account, url: account_url(@account), html: {novalidate: true, method: :put, id: "account_course_creation_settings", class: "account_settings"} do |f| %>
        <% if @account.primary_settings_root_account? && !@account.site_admin? %>
          <fieldset>
            <legend><%= t(:create_courses_title, "Who Can Create New Courses") %></legend>
            <% if @account.feature_enabled?(:create_course_subaccount_picker) %>
              <div id="course_creation_settings"></div>
            <% else %>
              <div style="font-size: 0.8em;"><%= t(:create_courses_description, "(Account Administrators can always create courses)") %></div>
              <%= f.fields_for :settings do |settings| %>
                <div>
                  <%= settings.check_box :teachers_can_create_courses, :checked => @account.teachers_can_create_courses? %>
                  <%= settings.label :teachers_can_create_courses, :en => "Teachers" %>
                </div>
                <div>
                  <%= settings.check_box :students_can_create_courses, :checked => @account.students_can_create_courses? %>
                  <%= settings.label :students_can_create_courses, :en => "Students" %>
                </div>
                <div>
                  <%= settings.check_box :no_enrollments_can_create_courses, :checked => @account.no_enrollments_can_create_courses? %>
                  <%= settings.label :no_enrollments_can_create_courses, :en => "Users with no enrollments" %>
                </div>
              <% end %>
            <% end %>
          </fieldset>

          <% if @account.root_account.feature_enabled?(:course_templates) %>
            <fieldset>
              <legend><%= t('Course Template') %></legend>
              <%
                disabled = []
                options = []

                if @account.root_account?
                  options << [t("No Template Selected"), nil]
                  disabled << nil unless @account.course_template_id.nil? || @account.grants_any_right?(@current_user, :delete_course_template, :edit_course_template)
                else
                  options << [t("Inherit (%{course_name})", course_name: @account.parent_account.effective_course_template&.name || t("No Template Selected")), nil ]
                  options << [t("No Template Selected"), 0]
                  disabled.concat([0, nil]) unless @account.course_template_id == 0 || @account.grants_any_right?(@current_user, :delete_course_template, :edit_course_template)
                end

                visible_templates = @account.root_account.all_courses.templates
                                            .order(Course.best_unicode_collation_key('courses.name'))
                                            .pluck(:name, :id)
                if @account.course_template_id && !@account.course_template_id.zero?  && !visible_templates.find { |_, id| @account.course_template_id == id }
                  visible_templates.unshift([@account.course_template.name, @account.course_template_id])
                end
                options.concat(visible_templates)

                unless @account.grants_right?(@current_user, :edit_course_template)
                  nonselected = visible_templates.map(&:last)
                  nonselected.delete(@account.course_template_id)
                  disabled.concat(nonselected)
                end
              %>
              <%= f.select :course_template_id, options, disabled: disabled %>
              <p><%= f.label :course_template_id, en: 'The selected course template will be used for all new courses' %></p>
            </fieldset>
          <% end %>
        <% end %>

        <footer class="sticky-footer">
          <div class="form-actions-sticky-footer">
              <button type="submit" class="Button Button--primary"><%= t(:update_settings_button, "Update Settings") %></button>
          </div>
        </footer>
      <% end %>
    </div>
  <% end %>

  <!-- BLOCKED EMOJIS TAB -->
  <% if can_do(@context, @current_user, :manage_account_settings) && !@account.site_admin? && @account.primary_settings_root_account? && @account.feature_allowed?(:submission_comment_emojis) && @account.grants_right?(@current_user, :manage_account_settings) %>
    <div id="tab-blocked-emojis">
      <h2 class="screenreader-only"><%= t('headings.blocked_emojis', "Blocked Emojis") %></h2>
      <%= form_for :account, url: account_url(@account), html: {novalidate: true, method: :put, id: "account_blocked_emojis_settings", class: "account_settings"} do |f| %>
        <div id="emoji-deny-list"></div>

        <footer class="sticky-footer">
          <div class="form-actions-sticky-footer">
              <button type="submit" class="Button Button--primary"><%= t(:update_settings_button, "Update Settings") %></button>
          </div>
        </footer>
      <% end %>
    </div>
  <% end %>

  <!-- REMAINING TABS (Users, Notifications, Announcements, etc.) -->
  <% if can_do(@context, @current_user, :manage_storage_quotas) %>
    <div id="tab-quotas">
      <h2 class="screenreader-only"><%= t('headings.quotas', "Account Quotas") %></h2>
      <div id="quotas_tab_content_mount_point"></div>
    </div>
  <% end %>

  <% if can_do(@context, @current_user, :manage_account_settings) && @account.root_account? && @account.feature_enabled?(:microsoft_group_enrollments_syncing) %>
    <div id="tab-integrations">
    </div>
  <% end %>

  <% if can_do(@context, @current_user, :manage_account_settings) && @account.primary_settings_root_account? && !@account.site_admin? %>
    <div id="tab-notifications" data-values="<%=
      default_name = @account.settings[:outgoing_email_default_name]
      {
        externalWarning: @account.settings[:external_notification_warning],
        customName: default_name,
        customNameOption: default_name.blank? ? "default" : "custom",
        defaultName: HostUrl.outgoing_email_default_name
      }.to_json
    %>"></div>
  <% end %>

  <div id="tab-users">
    <h2><%= t(:account_admins_title, "Account Admins") %></h2>
    <ul class="admins_list user_list list admins">
      <%= render :partial => 'account_user', :collection => @account_users %>
      <% available_roles = @context.available_account_roles(false, @current_user).sort_by(&:display_sort_index) %>
      <% unless available_roles.empty? %>
        <%= render :partial => 'account_user', :object => nil %>
      <% end %>
    </ul>
    <% unless available_roles.empty? %>
      <% js_bundle :user_lists %>
      <button type="button" class="add_users_link Button Button--primary"><i class="icon-plus"></i> <%= t('links.add_admins', %{Account Admins}) %></button>
      <%= form_tag account_add_account_user_url(@account), {:id => "enroll_users_form", :style => "display: none;"} do  %>
        <h2><%= t(:add_admin_title, "Add Account Admins") %></h2>
        <div style="margin-top: 5px;">
          <div>
            <%= label_tag :role_id, t("Add More") %>
            <select name="role_id" id="admin_role_id">
            <% available_roles.each do |role| %>
              <option value="<%= role.id %>"><%= AccountUser.readable_type(role.name) %></option>
            <% end %>
            </select>
          </div>
          <%= render :partial => 'shared/user_lists' %>
        </div>
        <div class="form-actions">
          <button type="button" class="Button go_back_button"><%= t('buttons.modify_users', %{Go back and edit the list of users}) %></button>
          <button type="button" class="Button cancel_button"><%= t('#buttons.cancel', %{Cancel}) %></button>
          <button type="button" class="Button Button---primary verify_syntax_button"><%= t('buttons.continue', %{Continue...}) %></button>
          <button type="submit" class="Button Button--primary add_users_button"><%= t('buttons.add_users', %{OK Looks Good, Add These Users}) %></button>
        </div>
      <% end %>
    <% end %>
  </div>

  <div id="tab-announcements">
    <h2><%= t(:global_announcements_title, "Global Announcements") %></h2>
    <% if can_do(@account, @current_user, :manage_alerts) %>
      <button id="add_announcement_button" class="Button Button--primary element_toggler add_notification_toggle_focus" title="<%= t('Add New Announcement') %>" aria-label="<%= t('Add New Announcement') %>">
        <i class="icon-plus"></i> <%= t("New Announcement") %>
      </button>
      <%= form_for :account_notification,
       url: account_account_notifications_url(@account),
       html: {
         id: "add_notification_form",
         class: "hidden_form",
         role: "region" } do |f| %>

        <div class="grid-row">

          <div class="col-xs-12 col-lg-6">
            <div class="ic-Form-control">
              <label for="account_notification_subject" class="ic-Label">
                <%= t("Title") %>
              </label>
              <%= f.text_field :subject, :class => 'ic-Input', :id => 'account_notification_subject' %>
            </div>
          </div>

          <div class="col-xs-12 col-lg-6">
            <div class="ic-Form-control">
              <label for="account_notification_icon" class="ic-Label">
                <%= t("Announcement type") %>
              </label>
              <select
                id="account_notification_icon"
                class="ic-Input"
                name="account_notification[icon]"
              >
                <%= options_for_select([[t("information"), "information", {:class=>"information"}],
                  [t("error"), "error", {:class=>"error"}],
                  [t("warning"), "warning", {:class=>"warning"}],
                  [t("question"), "question", {:class=>"question"}],
                  [t("calendar"), "calendar", {:class=>"calendar"}]]) %>
              </select>
            </div>
          </div>

        </div>

        <div class="ic-Form-control">
          <label class="ic-Label"><%= t("Message") %></label>
          <%= f.text_area :message, :id => "account_notification_message", :class => 'alert_message' %>
          <% if @account.site_admin? %>
            <p>
              <%= t(:interpolate_account_domain, "Enter \"{{ACCOUNT_DOMAIN}}\" to substitute the user's root account domain") %>
            </p>
            <p>
              <%= t(:interpolate_user_id, "Enter \"{{CANVAS_USER_ID}}\" to substitute the user's unique Canvas ID") %>
            </p>
          <% end %>
        </div>

        <% if @account.root_account.trust_exists? %>
          <div class="ic-Form-control">
            <%= f.check_box :domain_specific, :checked => !!@account.root_account.settings[:default_announcement_domain_specific], :id => "domain_specific" %>
            <%= f.label :domain_specific, t("Only show announcement on current account domain") %>
          </div>
        <% end %>

        <% if @account.site_admin? %>
          <div class="ic-Checkbox-group">
            <div class="ic-Form-control ic-Form-control--checkbox">
              <div id="survey_announcement_field">
                <input type="checkbox" name="account_notification[required_account_service]" id="account_notification_required_account_service" value="account_survey_notifications" />
                <label class="ic-Label" for="account_notification_required_account_service">
                  <%= t "Site Admin Only. Send to 1 / %{denominator} users in enabled accounts each month.", denominator: f.text_field(:months_in_display_cycle, value: AccountNotification.default_months_in_display_cycle, disabled: true) %>
                </label>
              </div>
            </div>
          </div>
        <% end %>
        <div class="ic-Form-control">
          <div class="ic-Label" id="aria-announcements-send-to-group">
            <%= t "Show to" %>
            <%= roles_message(@account) %>
          </div>
          <div class="grid-row">
            <div class="col-xs-12 col-lg-4">
              <i><%= t "Course roles" %></i>
              <div class="ic-Checkbox-group" role="group" aria-labelledby="aria-announcements-send-to-group">
                <% @course_roles.each do |r| %>
                  <div class="ic-Form-control ic-Form-control--checkbox">
                    <%= check_box_tag "account_notification_roles[]", r[:id], false, { :class => "account_notification_role_cbx", :id => "account_notification_role_#{r[:id]}_cbx" } %>
                    <label class="ic-Label" for="<%= "account_notification_role_#{r[:id]}_cbx" %>">
                      <%= r[:label] %>
                      <span class="screenreader-only">
                        <%=t "Send this announcement to users with the course role of %{role}", :role => r[:label] %>
                      </span>
                    </label>
                  </div>
                <% end %>
                <% if @account.root_account? %>
                  <div class="ic-Form-control ic-Form-control--checkbox">
                    <%= check_box_tag "account_notification_roles[]", "NilEnrollment", false, { :class => "account_notification_role_cbx", :id => "account_notification_role_NilEnrollment_cbx" } %>
                    <label class="ic-Label" for="<%= "account_notification_role_NilEnrollment_cbx" %>">
                      <%= t "Unenrolled users" %>
                      <span class="screenreader-only">
                        <%=t "Send this announcement to unenrolled users" %>
                      </span>
                    </label>
                  </div>
                <% end %>
              </div>
            </div>
            <div class="col-xs-12 col-lg-4">
              <i><%= t "Account roles" %></i>
              <div class="ic-Checkbox-group" role="group" aria-labelledby="aria-announcements-send-to-group">
                <% @account_roles.each do |r| %>
                  <div class="ic-Form-control ic-Form-control--checkbox">
                    <%= check_box_tag "account_notification_roles[]", r[:id], false, { :class => "account_notification_role_cbx", :id => "account_notification_role_#{r[:id]}_cbx" } %>
                    <label class="ic-Label" for="<%= "account_notification_role_#{r[:id]}_cbx" %>">
                      <%= r[:label] %>
                      <span class="screenreader-only">
                        <%=t "Send this announcement to users with the account role of %{role}", :role => r[:label] %>
                      </span>
                    </label>
                  </div>
                <% end %>

                <div class="ic-Form-control ic-Form-control--checkbox">
                  <%= f.check_box :is_public, :id => "account_notification_is_public" %>
                  <%= f.label :is_public, t("Make this announcement public (visible on login page for unauthenticated users)"), :class => "ic-Label", :for => "account_notification_is_public" %>
                </div>
              </div>
            </div>
          </div>
        </div>     

        <div class="grid-row teacher-options" style="display: none;">
          <div class="col-xs-12 col-lg-6">
            <div class="ic-Form-control">
              <label for="account_notification_category" class="ic-Label">
                <%= t("Category") %>
              </label>
              <select 
                id="account_notification_category" 
                class="ic-Input" 
                name="account_notification[category]"
                width="270px"
              >
                <%= options_for_select([[t("Select Category"), ""],
                  [t("Urgent"), "Urgent"],
                  [t("Event"), "Event"],
                  [t("Reminder"), "Reminder"],
                  [t("General"), "General"]]) %>
              </select>
            </div>
          </div>
          <div class="col-xs-12 col-lg-6">
            <div class="ic-Form-control">
              <label for="account_notification_priority" class="ic-Label">
                <%= t("Priority") %>
              </label>
              <select 
                id="account_notification_priority" 
                class="ic-Input" 
                name="account_notification[priority]"
                width="270px"
              >
                <%= options_for_select([[t("Select Priority"), ""],
                  ["1", 1], ["2", 2], ["3", 3], ["4", 4], ["5", 5]]) %>
              </select>
              <div class="ic-Form-help-text">
                <%= t("Priority scale: 5 is highest priority, 1 is lowest priority") %>
              </div>
            </div>
          </div>
        </div>   

        <div class="grid-row">

          <div class="col-xs-12 col-lg-6">

            <div class="ic-Form-control">
              <label class="ic-Label" id="announcement_starts_at_label">
                <%= t("Announcement starts at") %>
                <span class="screenreader-only">
                  <%= datepicker_screenreader_prompt %>
                </span>
              </label>
              <%= f.text_field :start_at,
                               :id => "account_notification_start_at",
                               :class => 'datetime_field',
                               "aria-labelledby" => "announcement_starts_at_label",
                               "data-tooltip" => "",
                               :title => accessible_date_format %>
            </div>
          </div>

          <div class="col-xs-12 col-lg-6">
            <div class="ic-Form-control">
              <label class="ic-Label" id="announcement_ends_at_label">
                <%= t("Announcement ends at") %>
                <span class="screenreader-only">
                  <%= datepicker_screenreader_prompt %>
                </span>
              </label>
              <%= f.text_field :end_at,
                               :id => "account_notification_end_at",
                               :class => 'datetime_field',
                               "aria-labelledby" => "announcement_ends_at_label",
                               "data-tooltip" => "",
                               :title => accessible_date_format %>
            </div>
          </div>

        </div>

        <% if !@account.site_admin? %>
          <div class="ic-Form-control ic-Form-control--checkbox">
            <%= f.check_box :send_message, :id => "account_notification_send_message" %>
            <%= f.label :send_message, t("Send notification directly to users when announcement starts"), :class => "ic-Label" %>
          </div>
        <% end %>

        <div class="ic-Form-actions">
          <button type="button" class="element_toggler Button Button--secondary add_notification_cancel_focus" aria-controls="add_notification_form">
            <%= t('#buttons.cancel', %{Cancel}) %>
          </button>
          <button type="submit" class="Button Button--primary">
            <%= t("Publish announcement") %>
          </button>
        </div>
      <% end %>
    <% end %>
    <ul class="announcements_list unstyled_list">
      <%= will_paginate(@announcements, params: {anchor: "tab-announcements"}) %>
      <% @announcements.each do |announcement| %>
        <li class="announcements_list_item">
          <div class="ic-notification ic-notification--admin-created ic-notification--<%= notification_container_classname(announcement) %>">
            <div class="ic-notification__icon" role="presentation">
              <i class="<%= notification_icon_classname(announcement) %>"></i>
              <span class="screenreader-only">
                <%= accessible_message_icon_text(notification_icon_type(announcement)) %>
              </span>
            </div>
            <div class="notification_account_content">
              <div class="ic-notification__content">
                <div class="ic-notification__message">
                  <h2 class="ic-notification__title notification_subject">
                    <%= announcement.subject %>
                  </h2>
                  <span class="notification_message">
                    <%= user_content(announcement.message) %>
                  </span>
                  <% if can_do(@account, @current_user, :manage_alerts) %>
                    <div class="ic-notification__admin-actions">
                      <button
                        type="button"
                        id="<%= "notification_copy_#{announcement.id}" %>"
                        title="<%= t("Copy announcement %{title}", title: announcement.subject) %>"
                        class="Button Button--icon-action element_toggler copy_notification_toggle_focus"
                        data-copy-toggle-id="<%= announcement.id %>"
                      >
                        <span class="screenreader-only"><%= t("Copy announcement %{title}", :title => announcement.subject) %></span>
                        <i class="icon-copy"></i>
                      </button>
                      <button
                        type="button"
                        id="<%= "notification_edit_#{announcement.id}" %>"
                        title="<%= t("Edit announcement %{title}", title: announcement.subject) %>"
                        class="Button Button--icon-action element_toggler edit_notification_toggle_focus"
                        aria-controls="<%= "edit_notification_form_#{announcement.id}" %>"
                        data-edit-toggle-id="<%= announcement.id %>"
                      >
                        <span class="screenreader-only"><%= t("Edit announcement %{title}", :title => announcement.subject) %></span>
                        <i class="icon-edit"></i>
                      </button>
                      <button
                        type="button"
                        data-url="<%= context_url(@account, :context_account_notification_url, announcement) %>"
                        class="Button Button--icon-action delete_notification_link"
                        title="<%= t("Delete announcement %{title}", title: announcement.subject) %>"
                      >
                        <span class="screenreader-only"><%= t("Delete announcement %{title}", :title => announcement.subject) %></span>
                        <i class="icon-trash"></i>
                      </button>
                    </div>
                  <% end %>
                </div>
              </div>
              <span class="notification_account_content_text">
                <% if @account.site_admin? %>
                  <%= t("This is a message from *Canvas Administration*", wrapper: '<b>\1</b>') %>
                <% else %>
                  <%= t("This is a message from *%{name}*", name: announcement.account.name, wrapper: '<b>\1</b>') %>
                <% end %>
              </span>
            </div>

          </div>
          <div class="announcement-details">
            <div class="announcement-details-post-info">
              <div class="announcement-details-post-info__datetime">
                <%= t(:alert_timespan, "from %{start_at} to %{end_at}",
                      :start_at => datetime_string(announcement.start_at),
                      :end_at => datetime_string(announcement.end_at)) %>
              </div>
              <div class="announcement-details-post-info__sender">
                <%= link_to(context_user_name(@account, announcement.user_id), user_path(announcement.user_id)) %>
              </div>
            </div>
            <% if announcement.required_account_service %>
              <div>
                <%= AccountServices.allowable_services[announcement.required_account_service.to_sym].try(:[], :name) %>
              </div>
            <% end %>
            <% unless announcement.account_notification_roles.empty? %>
              <div class="announcement-details-roles">
                <%= t "Send only to the following types of users:" %>
                <%= roles_message(@account) %>
                <ul>
                  <% announcement.account_notification_roles.each do |r| %>
                    <% @course_roles.select {|rt| rt[:id] == r.role_id }.each do |rt| %>
                      <li><%= rt[:label] %></li>
                    <% end %>
                    <% # NilEnrollment is a special case %>
                    <% if r.role_id.nil? %>
                      <li><%= t :no_enrollment_roles, "Unenrolled users" %></li>
                    <% end %>
                    <% @account_roles.select {|rt| rt[:id] == r.role_id }.each do |rt| %>
                      <li><%= rt[:label]  %></li>
                    <% end %>
                  <% end %>
                </ul>
              </div>
            <% end %>
            <% if announcement.months_in_display_cycle %>
              <div>
                <%= t :announcement_sent_to_subset, "Sent to 1 / %{denominator} users each month", denominator: announcement.months_in_display_cycle %>
              </div>
            <% end %>
            <% if can_do(@account, @current_user, :manage_alerts) %>
              <%= render(:partial => "edit_account_notification", :locals => {:announcement => announcement}) %>
            <% end %>
          </div>
        </li>
      <% end %>
    </ul>
  </div>
  <% if can_do(@context, @current_user, :read_reports) %>
    <div id="tab-reports">
      <p><%= t("Loading...") %></p>
      <!-- this will be populated by an xhr request to AccountsController::reports_tab -->
    </div>
  <% end %>
  <% if can_do(@context, @current_user, :read_as_admin) %>
      <div id="tab-tools">
        <%= render :partial => 'external_tools/external_tools' %>
      </div>
  <% end %>

  <% if @context.root_account.settings[:enable_alerts] && can_do(@context, @current_user, :manage_interaction_alerts) %>
      <div id="tab-alerts">
        <div id="alerts_mount_point"></div>
      </div>
  <% end %>


  <% if @account.root_account.feature_enabled?(:javascript_csp) && can_do(@context, @current_user, :manage_account_settings) && !@account.site_admin? %>
    <div id="tab-security"></div>
  <% end %>

</div>
<div id="rqd_mount"></div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
      // Get all course role checkboxes in order
      var courseRoleCheckboxes = document.querySelectorAll('.account_notification_role_cbx');
      var teacherOptions = document.querySelector('.teacher-options');

      // Get the second checkbox (index 1) which should be the Teacher checkbox
      var teacherCheckbox = courseRoleCheckboxes.length > 1 ? courseRoleCheckboxes[1] : null;

      if (teacherCheckbox && teacherOptions) {
        // Set initial state
        teacherOptions.style.display = teacherCheckbox.checked ? 'flex' : 'none';

        // Add change listener
        teacherCheckbox.addEventListener('change', function() {
          teacherOptions.style.display = this.checked ? 'flex' : 'none';
        });

        console.log('Teacher checkbox found at position 2:', teacherCheckbox.id);
      } else {
        console.error('Could not find teacher checkbox or options', {
          'courseRoleCheckboxes': courseRoleCheckboxes,
          'teacherCheckbox': teacherCheckbox,
          'teacherOptions': teacherOptions,
          'totalCheckboxes': courseRoleCheckboxes.length
        });
      }
    });
  </script>

